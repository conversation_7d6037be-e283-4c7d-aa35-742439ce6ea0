use std::time::Duration;

use tracing::info;

use crate::{
    configuration::NetworkConfiguration,
    connection::<PERSON><PERSON><PERSON><PERSON>,
    credentials::fetch_credentials,
    datacenter::ensure_primary_datacenter_is_detected,
    ports::{DnsResolverPort, HalPort, MsPort, ParamsPort, PingerPort, RandomGeneratorPort},
    retry::retry_async_until_success,
    watchdog::{TaskHealthMonitor, TaskId},
};

pub struct NetworkManager<
    HAL: HalPort,
    MS: MsPort,
    PING: PingerPort,
    RANDOM: RandomGeneratorPort,
    DNS: DnsResolverPort,
    PARAMS: ParamsPort,
> {
    pub hal: HAL,
    pub configuration: NetworkConfiguration,
    pub module_securite: MS,
    pub pinger: PING,
    pub random_generator: RANDOM,
    pub dns_resolver: DNS,
    pub params: PARAMS,
    pub health_monitor: Option<TaskHealthMonitor>,
}

impl<
        HAL: HalPort,
        MS: MsPort,
        PING: PingerPort,
        RANDOM: RandomGeneratorPort,
        DNS: DnsResolverPort,
        PARAMS: ParamsPort,
    > NetworkManager<HAL, MS, PING, RAND<PERSON>, DNS, PARAMS>
{
    pub async fn run(&mut self) {
        info!("i2r-network en cours de fonctionnement");

        // Send initial health heartbeat
        if let Some(health_monitor) = &self.health_monitor {
            health_monitor
                .heartbeat(&TaskId::new("network_manager"))
                .await;
        }

        info!("Récupération des identifiants de connexion en cours");
        let credentials =
            fetch_credentials(&self.hal, &self.configuration, &self.module_securite).await;

        // Send health heartbeat after credentials fetch
        if let Some(health_monitor) = &self.health_monitor {
            health_monitor
                .heartbeat(&TaskId::new("network_manager"))
                .await;
        }

        info!("Connexion du boitier en cours");
        retry_async_until_success(
            || {
                self.hal
                    .connect(&credentials.apn, &credentials.login, &credentials.password)
            },
            Duration::from_secs(5),
        )
        .await;

        // Send health heartbeat after connection
        if let Some(health_monitor) = &self.health_monitor {
            health_monitor
                .heartbeat(&TaskId::new("network_manager"))
                .await;
        }

        info!("Connexion réussie !");
        retry_async_until_success(
            || {
                ensure_primary_datacenter_is_detected(
                    &self.dns_resolver,
                    &self.params,
                    &self.configuration,
                )
            },
            Duration::from_secs(2),
        )
        .await;

        // Send health heartbeat after datacenter detection
        if let Some(health_monitor) = &self.health_monitor {
            health_monitor
                .heartbeat(&TaskId::new("network_manager"))
                .await;
        }

        self.configuration = self.params.fetch_configuration();
        info!("i2R-network est en cours de fonctionnement");

        let connection_handler = ConnectionHandler::new(
            self.pinger.clone(),
            self.random_generator.clone(),
            self.hal.clone(),
            credentials.clone(),
            self.configuration.clone(),
            self.health_monitor.clone(),
        );

        connection_handler.watch().await;
    }
}
