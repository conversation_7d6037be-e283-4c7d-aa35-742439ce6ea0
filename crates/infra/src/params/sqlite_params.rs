use std::{net::Ipv4Addr, sync::Arc, time::Duration};

use anyhow::Context;
use network::{
    configuration::{default_configuration, NetworkConfiguration},
    datacenter::Datacenter,
    errors::NetworkError,
    ports::ParamsPort,
};
use rusqlite::params;
use tracing::info;

#[derive(Clone)]
pub struct SqliteParamsAdapter {
    connection: Arc<rusqlite::Connection>,
}

impl SqliteParamsAdapter {
    #[tracing::instrument]
    pub fn connect(db_path: &str) -> Result<Self, NetworkError> {
        info!("connexion à la base de données en cours...");

        let connection =
            rusqlite::Connection::open(db_path).context("ouverture de la base de données")?;

        info!("Connexion réussie");
        let adapter = SqliteParamsAdapter {
            connection: Arc::new(connection),
        };

        adapter.ensure_table_is_created()?;

        Ok(adapter)
    }

    fn ensure_table_is_created(&self) -> Result<(), NetworkError> {
        let _ = self
            .connection
            .execute_batch(
                "CREATE TABLE IF NOT EXISTS parameters(
                    param_name text not null primary key,
                    param_value text not null,
                    updated boolean
                    );",
            )
            .context("mise à jour du paramètre en base")?;

        Ok(())
    }

    fn get_value(&self, key: &str) -> Result<String, NetworkError> {
        let value: String = self
            .connection
            .query_row(
                "SELECT param_value FROM parameters WHERE param_name=(?1)",
                [key],
                |r| r.get(0),
            )
            .context("récupération d'un paramètre")?;

        Ok(value)
    }

    fn get_value_in_seconds(&self, key: &str) -> Result<Duration, NetworkError> {
        Ok(Duration::from_secs(self.get_u64_value(key)?))
    }

    fn get_value_in_milliseconds(&self, key: &str) -> Result<Duration, NetworkError> {
        Ok(Duration::from_millis(self.get_u64_value(key)?))
    }

    fn get_u8_value(&self, key: &str) -> Result<u8, NetworkError> {
        Ok(self
            .get_value(key)?
            .parse::<u8>()
            .context("conversion en valeur numérique du param en bdd")?)
    }

    fn get_u64_value(&self, key: &str) -> Result<u64, NetworkError> {
        Ok(self
            .get_value(key)?
            .parse::<u64>()
            .context("conversion en valeur numérique du param en bdd")?)
    }

    pub fn get_ipv4_value(&self, key: &str) -> Result<Ipv4Addr, NetworkError> {
        Ok(self
            .get_value(key)?
            .parse::<Ipv4Addr>()
            .context("conversion d'une ipv4 en bdd")?)
    }

    /// Modifie le paramètre en base, et le rajoute si inexistant
    ///
    /// Il s'agit du mécanisme d'upsert de SQLite, plus d'informations via la
    /// ["documentation de SQLite"](https://www.sqlite.org/draft/lang_UPSERT.html)
    pub fn upsert_parameter(
        &self,
        param_name: &str,
        param_value: &str,
    ) -> Result<(), NetworkError> {
        let _ = self
            .connection
            .execute("INSERT INTO parameters(param_name, param_value) VALUES(?1, ?2) ON CONFLICT(param_name) DO UPDATE SET param_value=excluded.param_value", params![param_name, param_value])
            .context("mise à jour du paramètre en base")?;

        Ok(())
    }
}

impl ParamsPort for SqliteParamsAdapter {
    fn fetch_configuration(&self) -> NetworkConfiguration {
        let default_config = default_configuration();

        NetworkConfiguration {
            check_interval: self
                .get_value_in_seconds("i2r.network.connection-check-interval-secs")
                .unwrap_or(default_config.check_interval),
            ping_interval: self
                .get_value_in_milliseconds("i2r.si.ping.period-ms")
                .unwrap_or(default_config.ping_interval),
            ping_timeout: self
                .get_value_in_milliseconds("i2r.si.ping.timeout-ms")
                .unwrap_or(default_config.ping_timeout),
            waiting_delay_after_modem_reset: self
                .get_value_in_seconds(
                    "i2r.network.modem.connection.wait.seconds-delay-after-modem-reset",
                )
                .unwrap_or(default_config.waiting_delay_after_modem_reset),
            maximum_random_ping_delay: self
                .get_value_in_milliseconds("i2r.si.ping.maximum-random-delay-ms")
                .unwrap_or(default_config.maximum_random_ping_delay),
            ping_failures_limit_before_modem_reset: self
                .get_u8_value("i2r.si.ping.consecutive-failed-pings-limit-before-wan-reset")
                .unwrap_or(default_config.ping_failures_limit_before_modem_reset),
            modem_resets_before_bip_reboot: self
                .get_u8_value("i2r.si.ping.consecutive-modem-resets-limit-before-bip-reboot")
                .unwrap_or(default_config.modem_resets_before_bip_reboot),
            orange_domain: self
                .get_value("i2r.network.domain.orange")
                .unwrap_or(default_config.orange_domain),
            orange_apn: self
                .get_value("i2r.network.apn.orange")
                .unwrap_or(default_config.orange_apn),
            bouygues_apn: self
                .get_value("i2r.network.apn.bouygues")
                .unwrap_or(default_config.bouygues_apn),
            sfr_apn: self
                .get_value("i2r.network.apn.sfr")
                .unwrap_or(default_config.sfr_apn),
            pacy_ip: self
                .get_ipv4_value("i2r.si.ip.pacy")
                .unwrap_or(default_config.pacy_ip),
            noe_ip: self
                .get_ipv4_value("i2r.si.ip.noe")
                .unwrap_or(default_config.noe_ip),
            datacenter_netmask: self
                .get_ipv4_value("i2r.si.datacenter-netmask")
                .unwrap_or(default_config.datacenter_netmask),
            primary_datacenter: self
                .get_value("i2r.si.primary-datacenter")
                .ok()
                .and_then(datacenter_from_id),
            watchdog_interval_secs: self
                .get_value_in_seconds("i2r.watchdog-interval-secs.i2r-network")
                .unwrap_or(default_config.watchdog_interval_secs),
            task_health_timeout: self
                .get_value_in_seconds("i2r.watchdog.task-health-timeout-secs")
                .unwrap_or(default_config.task_health_timeout),
            network_manager_health_timeout: self
                .get_value_in_seconds("i2r.watchdog.network-manager-health-timeout-secs")
                .unwrap_or(default_config.network_manager_health_timeout),
            dbus_health_timeout: self
                .get_value_in_seconds("i2r.watchdog.dbus-health-timeout-secs")
                .unwrap_or(default_config.dbus_health_timeout),
        }
    }

    fn set_primary_datacenter(&self, primary_datacenter: &Datacenter) -> Result<(), NetworkError> {
        let datacenter_id = match primary_datacenter {
            Datacenter::NOE => "noe",
            Datacenter::PACY => "pacy",
        };

        // TODO : centraliser les noms des params, potentiellement généré automatiquement via I2R-506
        self.upsert_parameter("i2r.si.primary-datacenter", datacenter_id)
    }
}

fn datacenter_from_id(datacenter_id: String) -> Option<Datacenter> {
    match datacenter_id.to_lowercase().as_str() {
        "noe" => Some(Datacenter::NOE),
        "pacy" => Some(Datacenter::PACY),
        _ => None,
    }
}
